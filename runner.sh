#!/bin/bash

# G<PERSON><PERSON>abel Runner Script
# Script to manage Django development server with start, stop, restart commands

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/config.json"
MANAGE_PY="$SCRIPT_DIR/manage.py"
PID_FILE="$SCRIPT_DIR/server.pid"
LOG_FILE="$SCRIPT_DIR/server.log"

# Read host and port from config.json
read_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        # Simple parsing without jq dependency
        HOST=$(grep -o '"host"[[:space:]]*:[[:space:]]*"[^"]*"' "$CONFIG_FILE" | cut -d'"' -f4)
        PORT=$(grep -o '"port"[[:space:]]*:[[:space:]]*[0-9]*' "$CONFIG_FILE" | grep -o '[0-9]*')
        HOST=${HOST:-"0.0.0.0"}
        PORT=${PORT:-"9924"}
    else
        HOST="0.0.0.0"
        PORT="9924"
    fi
}

# Check if server is running
is_running() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            # PID file exists but process is not running, clean up
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# Start the server
start_server() {
    if is_running; then
        echo -e "${YELLOW}⚠️  Server is already running (PID: $(cat "$PID_FILE"))${NC}"
        echo -e "${GREEN}📍 Server is available at: http://$HOST:$PORT${NC}"
        return 0
    fi

    echo -e "${BLUE}🚀 Starting GGECLabel Development Server...${NC}"
    
    # Check if virtual environment exists and activate it
    if [[ -d "$SCRIPT_DIR/venv" ]]; then
        echo -e "${BLUE}🔧 Activating virtual environment...${NC}"
        if source "$SCRIPT_DIR/venv/bin/activate"; then
            echo -e "${GREEN}✅ Virtual environment activated${NC}"
        else
            echo -e "${YELLOW}⚠️  Failed to activate virtual environment, continuing...${NC}"
        fi
    else
        echo -e "${BLUE}ℹ️  No virtual environment found at $SCRIPT_DIR/venv${NC}"
    fi

    # Check if manage.py exists
    if [[ ! -f "$MANAGE_PY" ]]; then
        echo -e "${RED}❌ Error: manage.py not found!${NC}"
        exit 1
    fi

    # Start the Django development server in background
    echo -e "${BLUE}🔧 Starting Django server: python $MANAGE_PY runserver $HOST:$PORT${NC}"
    nohup python "$MANAGE_PY" runserver "$HOST:$PORT" > "$LOG_FILE" 2>&1 &
    local pid=$!

    # Save PID to file
    echo "$pid" > "$PID_FILE"
    echo -e "${BLUE}📝 Server PID: $pid (saved to $PID_FILE)${NC}"

    # Wait a moment to check if server started successfully
    echo -e "${BLUE}⏳ Waiting for server to start...${NC}"
    sleep 3

    if is_running; then
        echo -e "${GREEN}✅ Server started successfully (PID: $pid)${NC}"
        echo -e "${GREEN}📍 Server is available at: http://$HOST:$PORT${NC}"
        echo -e "${BLUE}📝 Logs are written to: $LOG_FILE${NC}"
        echo -e "${BLUE}🛑 Use './runner.sh stop' to stop the server${NC}"
    else
        echo -e "${RED}❌ Failed to start server. Check logs: $LOG_FILE${NC}"
        echo -e "${BLUE}📋 Last few lines of log:${NC}"
        if [[ -f "$LOG_FILE" ]]; then
            tail -5 "$LOG_FILE" | sed 's/^/  /'
        fi
        rm -f "$PID_FILE"
        return 1
    fi
}

# Stop the server
stop_server() {
    if ! is_running; then
        echo -e "${YELLOW}⚠️  Server is not running${NC}"
        return 0
    fi

    local pid=$(cat "$PID_FILE")
    echo -e "${BLUE}🛑 Stopping GGECLabel Development Server (PID: $pid)...${NC}"

    # Try graceful shutdown first
    if kill "$pid" 2>/dev/null; then
        echo -e "${BLUE}📤 Sent SIGTERM to process $pid${NC}"
    else
        echo -e "${YELLOW}⚠️  Could not send SIGTERM to process $pid${NC}"
    fi

    # Wait for process to stop
    local count=0
    while ps -p "$pid" > /dev/null 2>&1 && [[ $count -lt 10 ]]; do
        echo -e "${BLUE}⏳ Waiting for process to stop... ($((count+1))/10)${NC}"
        sleep 1
        ((count++))
    done

    # Force kill if still running
    if ps -p "$pid" > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Forcing server shutdown...${NC}"
        if kill -9 "$pid" 2>/dev/null; then
            echo -e "${BLUE}📤 Sent SIGKILL to process $pid${NC}"
        else
            echo -e "${YELLOW}⚠️  Could not send SIGKILL to process $pid${NC}"
        fi
        sleep 1
    fi

    # Clean up PID file
    rm -f "$PID_FILE"

    # Check final status
    if ! ps -p "$pid" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Server stopped successfully${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to stop server (process $pid still running)${NC}"
        # Don't exit here in restart context, just return error code
        return 1
    fi
}

# Restart the server
restart_server() {
    echo -e "${BLUE}🔄 Restarting GGECLabel Development Server...${NC}"

    # Stop the server (don't exit on failure)
    if is_running; then
        if ! stop_server; then
            echo -e "${YELLOW}⚠️  Stop failed, but continuing with restart...${NC}"
        fi
    else
        echo -e "${BLUE}ℹ️  Server was not running${NC}"
    fi

    # Wait a bit longer for cleanup
    sleep 2

    # Start the server
    echo -e "${BLUE}🚀 Starting server...${NC}"
    start_server
}

# Show server status
show_status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        echo -e "${GREEN}✅ Server is running (PID: $pid)${NC}"
        echo -e "${GREEN}📍 Server is available at: http://$HOST:$PORT${NC}"
        echo -e "${BLUE}📝 Logs: $LOG_FILE${NC}"
    else
        echo -e "${RED}❌ Server is not running${NC}"
    fi
}

# Show usage
show_usage() {
    echo "Usage: $0 {start|stop|restart|status}"
    echo ""
    echo "Commands:"
    echo "  start    - Start the Django development server in background"
    echo "  stop     - Stop the running server"
    echo "  restart  - Restart the server (stop + start)"
    echo "  status   - Show server status"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 stop"
    echo "  $0 restart"
    echo "  $0 status"
}

# Main script logic
main() {
    read_config
    
    case "${1:-}" in
        start)
            start_server
            ;;
        stop)
            stop_server
            ;;
        restart)
            restart_server
            ;;
        status)
            show_status
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
