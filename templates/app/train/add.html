{% extends "app/base_site.html" %}

{% block title %}  添加训练 {% endblock title %}

{% block stylesheets %}
  {{ block.super }}

{% endblock stylesheets %}

{% block content %}

  <div class="right_col" role="main">
    <div class="">
      <div class="row">

          <div class="col-md-12 col-sm-12 col-xs-12">
            <div class="x_panel">

              <div class="x_title">
               <h2> 添加训练
                    <span id="top_loading" ><img class="top_loading_img" src="/static/images/load.gif" alt="loading">加载中</span>
                    <span id="top_msg">{{top_msg}}</span>
               </h2>
                  <div class="clearfix"></div>
              </div>
              <div class="x_content">
                <!--<p><code>说明</code> 系统支持的行为算法</p>-->
                       <div class="form-horizontal form-label-left">
                        <!--
                        <p>For alternative validation library <code>parsleyJS</code> check out in the <a href="form.html">form page</a>
                        </p>
                        <span class="section">Personal Info</span>
                        -->

                          <div class="item form-group">
                          <label class="control-label col-md-3 col-sm-3 col-xs-12">训练编号
                          </label>
                            <div class="col-md-6 col-sm-6 col-xs-12">
                                <input type="text" readonly  value="{{ train_code }}" required="required" class="form-control col-md-6 col-xs-12">
                              </div>
                            </div>


                        <div class="item form-group">
                              <label class="control-label col-md-3 col-sm-3 col-xs-12" >选择任务 <span class="required" style="color: red;">*</span>
                              </label>
                            <div class="col-md-6 col-sm-6 col-xs-12">
                                    <select id="select_task" class="select2_single form-control" required="required">
                                        <option value="0">请选择</option>
                                        {% for task in tasks %}
                                            <option  {% if  task_code == task.code %}selected{% endif %} value="{{ task.code }}">{{ task.name }}（{{ task.code }}）{{ task.sample_annotation_count }}/{{ task.sample_count }}</option>
                                        {% endfor %}
                                    </select>

                              </div>
                        </div>

                       <div class="item form-group">
                              <label class="control-label col-md-3 col-sm-3 col-xs-12" >选择算法 <span class="required" style="color: red;">*</span>
                              </label>
                            <div class="col-md-6 col-sm-6 col-xs-12">

                                <select id="select_algorithm" class="select2_single form-control" required="required">
                                    <option value="0">请选择</option>
                                    {% for algorithm in algorithms %}
                                        <option value="{{ algorithm.code }}">{{ algorithm.name }}</option>
                                     {% endfor %}

                                </select>

                              </div>
                        </div>

                          <div class="item form-group">
                          <label class="control-label col-md-3 col-sm-3 col-xs-12">推理设备 <span class="required" style="color: red;">*</span>
                          </label>
                        <div class="col-md-6 col-sm-6 col-xs-12">

                                    <input type="text" id="device"  value="gpu" required="required" class="form-control col-md-6 col-xs-12">

                          </div>
                        </div>

                                                          <div class="item form-group">
                          <label class="control-label col-md-3 col-sm-3 col-xs-12" >输入尺寸 <span class="required" style="color: red;">*</span>
                          </label>
                        <div class="col-md-6 col-sm-6 col-xs-12">

                            <input type="number" id="imgsz"  value="640" required="required" class="form-control col-md-6 col-xs-12">

                          </div>
                        </div>


                        <div class="item form-group">
                          <label class="control-label col-md-3 col-sm-3 col-xs-12">训练周期 <span class="required" style="color: red;">*</span>
                          </label>
                        <div class="col-md-6 col-sm-6 col-xs-12">

                            <input type="number" id="epochs"  value="100" required="required" class="form-control col-md-6 col-xs-12">

                          </div>
                        </div>

                        <div class="item form-group">
                          <label class="control-label col-md-3 col-sm-3 col-xs-12">训练批次 <span class="required" style="color: red;">*</span>
                          </label>
                        <div class="col-md-6 col-sm-6 col-xs-12">

                            <input type="number" id="batch"  value="16" required="required" class="form-control col-md-6 col-xs-12">

                          </div>
                        </div>

                                        <div class="item form-group">
                          <label class="control-label col-md-3 col-sm-3 col-xs-12">保存周期 <span class="required" style="color: red;">*</span>
                          </label>
                        <div class="col-md-6 col-sm-6 col-xs-12">

                            <input type="number" id="save_period"  value="20" required="required" class="form-control col-md-6 col-xs-12">

                          </div>
                        </div>

                          <div class="item form-group">
                              <label class="control-label col-md-3 col-sm-3 col-xs-12" >训练比 <span class="required" style="color: red;">*</span>
                              </label>
                            <div class="col-md-6 col-sm-6 col-xs-12">

                                             <input type="number" id="sample_ratio"  value="5" required="required" class="form-control col-md-6 col-xs-12">


                              </div>
                        </div>

                        <div class="item form-group">
                          <label class="control-label col-md-3 col-sm-3 col-xs-12">其他参数
                          </label>
                        <div class="col-md-6 col-sm-6 col-xs-12">

                           <textarea rows="3" id="extra" class="form-control col-md-6 col-xs-12" placeholder="其他参数"></textarea>


                          </div>
                        </div>
                        <!-- buttons start -->
                        <div class="form-group">
                             <div class="col-md-6 col-md-offset-3">

                                    <button type="button" onclick="window.history.go(-1)" class="btn btn-primary">返回</button>
                                     <button type="button" onclick="f_add()" class="btn btn-success">提交</button>


                          </div>
                        </div>
                         <!-- buttons end -->

                      </div>
              </div>
            </div>
          </div>

      </div>

    </div>
  </div>

{% endblock content %}

{% block javascripts %}
  {{ block.super }}

<script>
    let mTrainCode = "{{ train_code }}";
    let ele_top_loading = $("#top_loading");
    let ele_top_msg= $("#top_msg");
    let eleSelectTask = $("#select_task");// select  选择任务
    let eleSelectAlgorithm = $("#select_algorithm");// select
    function f_manage(train_code) {
        let url = "/train/manage?code="+train_code;
        window.location.href = url;
    }
    function f_add() {
        let task_code = eleSelectTask.val().trim();//typeof string

        if(task_code==="0"){
            myAlert("请选择任务","error");
            return;
        }
        let algorithm_code = eleSelectAlgorithm.val().trim();
        if(algorithm_code==="0"){
            myAlert("请选择算法","error");
            return;
        }
        let device = $("#device").val().trim();
        let imgsz = parseInt($("#imgsz").val());
        let epochs = parseInt($("#epochs").val());
        let batch = parseInt($("#batch").val());
        let save_period = parseInt($("#save_period").val());
        let sample_ratio = parseInt($("#sample_ratio").val());
        let extra = $("#extra").val().trim();

        ele_top_loading.show();
        $.ajax({
               url: '/train/add',
               type: "post",
               async: true,
               data: {"task_code":task_code,
                   "train_code":mTrainCode,
                   "algorithm_code":algorithm_code,
                   "device":device,
                   "imgsz":imgsz,
                   "epochs":epochs,
                   "batch":batch,
                   "save_period":save_period,
                   "sample_ratio":sample_ratio,
                   "extra":extra
               },
               dataType: "json",
               timeout: 0,
               error: function () {
                   ele_top_loading.hide();
                   myAlert("网络异常，请确定网络正常！","error");

               },
               success: function (res) {

                   ele_top_loading.hide();
                   if(1000 === res.code){
                        myAlert(res.msg,"success",1000);
                           setTimeout(function() {
                               f_manage(mTrainCode);
                            }, 1000);

                   }else{
                        myAlert(res.msg,"error");
                   }
               }
            });

    }



</script>

{% endblock javascripts %}

