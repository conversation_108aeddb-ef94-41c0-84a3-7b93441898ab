import os
import shutil
import time
import zipfile
import tempfile
from datetime import datetime
from PIL import Image
from app.views.ViewsBase import *
from app.models import *
from django.shortcuts import render, redirect
from app.utils.Utils import buildPageLabels, gen_random_code_s
from app.utils.UploadUtils import UploadUtils
from app.utils.TrainUtils import TrainUtils
from app.utils.YoloPredict import YoloPredict
from app.permissions import require_permission, is_annotator_only, has_permission


def index(request):
    # 检查权限：管理员或标注员都可以访问，但标注员只能看到标注任务
    user = readUser(request)
    if not user:
        return render(request, 'app/message.html', {
            "msg": "请先登录",
            "is_success": False,
            "redirect_url": "/login"
        })

    # 检查用户是否有任务管理权限或标注权限
    if not (has_permission(user, 'task_manage') or has_permission(user, 'annotation_only')):
        return render(request, 'app/message.html', {
            "msg": "无权限访问任务管理",
            "is_success": False,
            "redirect_url": "/"
        })
    context = {

    }
    data = []

    params = parse_get_params(request)

    page = params.get('p', 1)
    page_size = params.get('ps', 10)
    try:
        page = int(page)
    except:
        page = 1

    try:
        page_size = int(page_size)
        if page_size > 20 or page_size < 10:
            page_size = 10
    except:
        page_size = 10

    skip = (page - 1) * page_size

    # 如果是标注员，只显示有样本需要标注的任务
    if is_annotator_only(user):
        # 只显示有未标注样本的任务
        sql_data = """
            select t.* from xc_task t
            where exists (
                select 1 from xc_task_sample s
                where s.task_code = t.code and s.annotation_state = 0
            )
            order by t.id desc limit %d,%d
        """ % (skip, page_size)
        sql_data_num = """
            select count(distinct t.id) as count from xc_task t
            where exists (
                select 1 from xc_task_sample s
                where s.task_code = t.code and s.annotation_state = 0
            )
        """
    else:
        # 管理员可以看到所有任务
        sql_data = "select * from xc_task order by id desc limit %d,%d " % (
            skip, page_size)
        sql_data_num = "select count(id) as count from xc_task "

    count = g_database.select(sql_data_num)

    if len(count) > 0:
        count = int(count[0]["count"])
        data = g_database.select(sql_data)
    else:
        count = 0

    page_num = int(count / page_size)  # 总页数
    if count % page_size > 0:
        page_num += 1
    pageLabels = buildPageLabels(page=page, page_num=page_num)
    pageData = {
        "page": page,
        "page_size": page_size,
        "page_num": page_num,
        "count": count,
        "pageLabels": pageLabels
    }

    context["data"] = data
    context["pageData"] = pageData
    context["storageDir_www"] = g_config.storageDir_www
    context["is_annotator"] = is_annotator_only(user)

    return render(request, 'app/task/index.html', context)


@require_permission('task_manage')
def add(request):
    if "POST" == request.method:
        __ret = False
        __msg = "未知错误"

        params = parse_post_params(request)
        handle = params.get("handle")
        code = params.get("code", "").strip()
        task_type = int(params.get("task_type",0))
        name = params.get("name", "").strip()
        remark = params.get("remark", "").strip()

        if "add" == handle and code:
            try:
                if name == "":
                    raise Exception("任务名称不能为空")

                if len(Task.objects.filter(code=code)) > 0:
                    raise Exception("任务编号已经存在")

                g_database.execute("update xc_task_sample set state=1 where task_code='%s'" % code)

                sample_count = g_database.select("select count(id) as count from xc_task_sample where task_code='%s'" % code)
                sample_count = int(sample_count[0]["count"])

                user = readUser(request)

                task = Task()
                task.user_id = user.get("id")
                task.username = user.get("username")
                task.sort = 0
                task.code = code
                task.task_type = task_type
                task.name = name
                task.remark = remark

                task.sample_annotation_count = 0
                task.sample_count = sample_count

                task.create_time = datetime.now()
                task.create_timestamp = int(time.time())
                task.last_update_time = datetime.now()
                task.state = 0
                task.save()



                __msg = "添加成功"
                __ret = True

            except Exception as e:
                __msg = str(e)
        else:
            __msg = "请求参数不完整"

        res = {
            "code": 1000 if __ret else 0,
            "msg": __msg
        }
        return HttpResponseJson(res)


    else:
        context = {

        }
        task_code = "task" + datetime.now().strftime("%Y%m%d%H%M%S")  # 随机生成一个训练编号
        context["handle"] = "add"
        context["storageDir_www"] = g_config.storageDir_www
        context["task"] = {
            "code": task_code,
            "task_type": 1
        }

        return render(request, 'app/task/add.html', context)


@require_permission('task_manage')
def edit(request):
    if "POST" == request.method:
        __ret = False
        __msg = "未知错误"

        params = parse_post_params(request)
        handle = params.get("handle")
        code = params.get("code", "").strip()
        name = params.get("name", "").strip()
        remark = params.get("remark", "").strip()

        if "edit" == handle and code:
            try:
                task = Task.objects.filter(code=code)
                if len(task) > 0:
                    task = task[0]
                else:
                    raise Exception("该任务不存在")

                sample_count, sample_annotation_count = f_readSampleCountAndAnnotationCount(task_code=code)

                task.name = name
                task.remark = remark
                task.sample_annotation_count = sample_annotation_count
                task.sample_count = sample_count

                task.save()


                __msg = "编辑成功"
                __ret = True

            except Exception as e:
                __msg = str(e)
        else:
            __msg = "请求参数不完整"

        res = {
            "code": 1000 if __ret else 0,
            "msg": __msg
        }
        return HttpResponseJson(res)

    else:
        context = {

        }
        params = parse_get_params(request)
        code = params.get("code")
        if code:
            task = Task.objects.filter(code=code)
            if len(task) > 0:
                task = task[0]
                context["handle"] = "edit"
                context["storageDir_www"] = g_config.storageDir_www
                context["task"] = task
            else:
                return render(request, 'app/message.html',
                              {"msg": "请通过任务管理进入", "is_success": False, "redirect_url": "/task/index"})

            return render(request, 'app/task/add.html', context)
        else:
            return redirect("/task/index")

def api_sync(request):
    ret = False
    msg = "未知错误"
    if request.method == 'GET':
        params = parse_get_params(request)

        tasks = Task.objects.all()
        for task in tasks:
            sample_count, sample_annotation_count = f_readSampleCountAndAnnotationCount(task_code=task.code)
            task.sample_count = sample_count
            task.sample_annotation_count = sample_annotation_count
            task.save()
        ret = True
        msg = "success"

    else:
        msg = "request method not supported"

    res = {
        "code": 1000 if ret else 0,
        "msg": msg
    }
    return HttpResponseJson(res)

def api_postDel(request):
    ret = False
    msg = "未知错误"
    if request.method == 'POST':
        params = parse_post_params(request)
        task_code = params.get("code", "").strip()
        task = Task.objects.filter(code=task_code)
        if len(task) > 0:
            task = task[0]

            del_sql = "delete from xc_task_sample where task_code='%s'" % task_code
            if not g_database.execute(del_sql):
                g_logger.error("del_sql=%s" % del_sql)

            task_dir = "%s/task/%s" % (g_config.storageDir, task_code)
            try:
                if os.path.exists(task_dir):
                    shutil.rmtree(task_dir)
            except Exception as e:
                g_logger.error("api_postDel task_code=%s,task_dir=%s,e=%s"%(task_code,task_dir,str(e)))

            task.delete()
            ret = True
            msg = "删除成功"

        else:
            msg = "数据不存在！"
    else:
        msg = "request method not supported"

    res = {
        "code": 1000 if ret else 0,
        "msg": msg
    }
    return HttpResponseJson(res)

def api_export(request):
    """导出YOLO格式数据集"""
    ret = False
    msg = "未知错误"
    data = {}

    if request.method == 'POST':
        try:
            params = parse_post_params(request)
            task_code = params.get("code", "").strip()

            if not task_code:
                raise Exception("任务代码不能为空")

            # 获取任务信息
            task = Task.objects.filter(code=task_code).first()
            if not task:
                raise Exception("任务不存在")

            # 创建临时目录，使用任务名称
            temp_dir = tempfile.mkdtemp(prefix="yolo_export_")
            # 生成安全的目录名
            task_name_safe = "".join(c for c in task.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            task_name_safe = task_name_safe.replace(' ', '_').lower()
            export_dir = os.path.join(temp_dir, task_name_safe)

            try:
                g_logger.info(f"开始导出任务: {task_code}, 存储目录: {g_config.storageDir}")

                # 导出YOLO格式数据
                success, export_msg, export_info = TrainUtils.export_yolo_dataset(
                    task=task,
                    storage_dir=g_config.storageDir,
                    export_dir=export_dir
                )

                g_logger.info(f"导出结果: success={success}, msg={export_msg}, info={export_info}")

                if not success:
                    raise Exception(export_msg)

                # 创建ZIP文件，使用任务名称
                zip_filename = f"{task_name_safe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
                zip_path = os.path.join(g_config.storageTempDir, zip_filename)

                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(export_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            # 计算相对路径
                            arcname = os.path.relpath(file_path, export_dir)
                            zipf.write(file_path, arcname)

                # 生成下载URL
                download_url = f"/storage/download?filename=temp/{zip_filename}"

                data = {
                    "download_url": download_url,
                    "filename": zip_filename,
                    "export_info": export_info
                }

                ret = True
                msg = f"导出成功！共导出 {export_info['exported_count']} 个样本（训练集: {export_info['train_count']}, 验证集: {export_info['val_count']}），{export_info['label_count']} 个标签类别"

            finally:
                # 清理临时目录
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass

        except Exception as e:
            msg = str(e)
            g_logger.error(f"导出YOLO数据集失败: {msg}")
    else:
        msg = "请求方法不支持"

    res = {
        "code": 1000 if ret else 0,
        "msg": msg,
        "data": data
    }
    return HttpResponseJson(res)


@require_permission('task_manage')
def api_model_predict(request):
    """使用YOLO模型对任务进行预标注"""
    ret = False
    msg = "未知错误"
    data = {}

    if request.method == 'POST':
        try:
            params = parse_post_params(request)
            task_code = params.get("code", "").strip()
            model_path = params.get("model_path", "").strip()

            if not task_code:
                raise Exception("任务代码不能为空")

            if not model_path:
                raise Exception("模型路径不能为空")

            # 获取任务信息
            task = Task.objects.filter(code=task_code).first()
            if not task:
                raise Exception("任务不存在")

            g_logger.info(f"开始模型预标注: 任务={task_code}, 模型={model_path}")

            # 执行模型预标注
            success, predict_msg, predict_info = YoloPredict.predict_task_samples(
                task=task,
                model_path=model_path,
                storage_dir=g_config.storageDir
            )

            g_logger.info(f"预标注结果: success={success}, msg={predict_msg}, info={predict_info}")

            if not success:
                raise Exception(predict_msg)

            # 更新任务的标注统计
            sample_count, sample_annotation_count = f_readSampleCountAndAnnotationCount(task_code=task.code)
            task.sample_count = sample_count
            task.sample_annotation_count = sample_annotation_count
            task.save()

            data = predict_info
            ret = True
            msg = predict_msg

        except Exception as e:
            msg = str(e)
            g_logger.error(f"模型预标注失败: {msg}")
    else:
        msg = "请求方法不支持"

    res = {
        "code": 1000 if ret else 0,
        "msg": msg,
        "data": data
    }
    return HttpResponseJson(res)
