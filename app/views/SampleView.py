import os
import time
from datetime import datetime
import random
import uuid
from app.views.ViewsBase import *
from app.models import *
from django.shortcuts import render, redirect
from app.utils.Utils import buildPageLabels, gen_random_code_s
from app.utils.UploadUtils import UploadUtils
from app.permissions import require_permission, is_annotator_only, has_permission
import cv2

def generate_unique_sample_code(max_retries=10):
    """
    生成唯一的样本代码
    使用微秒级时间戳 + UUID的一部分来确保唯一性
    """
    for attempt in range(max_retries):
        # 使用微秒级时间戳
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")[:-3]  # 去掉最后3位微秒，保留毫秒
        # 使用UUID的前8位作为随机部分
        uuid_part = str(uuid.uuid4()).replace('-', '')[:8]
        sample_code = f"sample{timestamp}{uuid_part}"

        # 检查数据库中是否已存在
        existing = TaskSample.objects.filter(code=sample_code).first()
        if not existing:
            return sample_code

        # 如果重复，等待1毫秒后重试
        time.sleep(0.001)

    # 如果所有重试都失败，使用完整UUID作为后备
    fallback_code = f"sample{datetime.now().strftime('%Y%m%d%H%M%S')}{str(uuid.uuid4()).replace('-', '')}"
    return fallback_code

def index(request):
    # 检查权限：标注员或有样本管理权限的用户都可以访问
    user = readUser(request)
    if not user:
        return render(request, 'app/message.html', {
            "msg": "请先登录",
            "is_success": False,
            "redirect_url": "/login"
        })

    # 标注员或有样本管理权限的用户可以访问
    if not (has_permission(user, 'annotation_only') or has_permission(user, 'sample_manage')):
        return render(request, 'app/message.html', {
            "msg": "无权限访问标注工具，请联系管理员分配权限",
            "is_success": False,
            "redirect_url": "/"
        })

    context = {

    }
    params = parse_get_params(request)

    task_code = params.get('task_code',"").strip()
    sample_code = params.get('sample_code',"").strip()

    context["task_code"] = task_code
    context["sample_code"] = sample_code
    context["storageDir_www"] = g_config.storageDir_www

    return render(request, 'app/sample/index.html', context)

def api_getIndex(request):
    # 检查权限：标注员或有样本管理权限的用户都可以获取索引
    user = readUser(request)
    if not user:
        res = {"code": 0, "msg": "请先登录"}
        return HttpResponseJson(res)

    if not (has_permission(user, 'annotation_only') or has_permission(user, 'sample_manage')):
        res = {"code": 0, "msg": "无权限获取样本列表"}
        return HttpResponseJson(res)

    ret = False
    msg = "未知错误"

    params = parse_get_params(request)
    page = params.get('p', 1)
    page_size = params.get('ps', 10)
    task_code = params.get('task_code', "").strip()

    try:
        page = int(page)
    except:
        page = 1
    try:
        page_size = int(page_size)
        if page_size > 20 or page_size < 10:
            page_size = 10
    except:
        page_size = 10


    skip = (page - 1) * page_size
    sql_data = "select * from xc_task_sample where task_code='%s' order by id asc limit %d,%d " % (task_code,skip, page_size)
    count = g_database.select("select count(id) as count from xc_task_sample where task_code='%s'" % task_code)
    count = int(count[0]["count"])
    if count > 0:
        data = g_database.select(sql_data)
    else:
        data = []

    page_num = int(count / page_size)  # 总页数
    if count % page_size > 0:
        page_num += 1

    pageLabels = buildPageLabels(page=page, page_num=page_num)
    pageData = {
        "page": page,
        "page_size": page_size,
        "page_num": page_num,
        "count": count,
        "pageLabels": pageLabels
    }
    ret = True
    msg = "success"

    res = {
        "code": 1000 if ret else 0,
        "msg": msg,
        "data": data,
        "pageData": pageData
    }
    return HttpResponseJson(res)


@require_permission('sample_manage')
def api_postAdd(request):
    ret = False
    msg = "未知错误"
    if request.method == 'POST':
        params = parse_post_params(request)
        task_code = params.get("task_code", "").strip()
        task_type = int(params.get("task_type", 0))
        upload_type = int(params.get("upload_type", 0)) # 1:图片文件 2:图片文件夹 3:视频文件 4:labelme文件夹

        task = Task.objects.filter(code=task_code).first()
        task_state = 1 if task else 0
        user = readUser(request)

        success_count = 0
        error_count = 0
        labelme_jpg_dict = {}
        labelme_json_dict = {}

        upload_utils = UploadUtils()
        filenames = request.FILES.keys()
        for filename in filenames:
            file = request.FILES.get(filename)
            if upload_type == 1 or upload_type == 2:
                __ret, __msg, __info = upload_utils.upload_sample_image(storageDir=g_config.storageDir, task_code=task_code, file=file)

                if __ret:
                    old_filename = __info.get("old_filename")
                    new_filename = __info.get("new_filename")

                    sample_code = generate_unique_sample_code()  # 生成唯一的样本编号

                    sample = TaskSample()
                    sample.sort = 0
                    sample.code = sample_code
                    sample.user_id = user.get("id")
                    sample.username = user.get("username")
                    sample.task_type = task_type
                    sample.task_code = task_code
                    sample.old_filename = old_filename
                    sample.new_filename = new_filename
                    sample.remark = ""
                    sample.create_time = datetime.now()
                    sample.state = task_state
                    sample.annotation_user_id = 0
                    sample.annotation_state = 0
                    sample.save()

                    success_count += 1
                else:
                    error_count += 1
            elif upload_type == 3:
                __ret, __msg, __info = upload_utils.upload_sample_video(storageDir=g_config.storageDir,
                                                                        task_code=task_code, file=file)
                #print(__ret,__msg,__info)
                if __ret:
                    old_filename = __info.get("old_filename")
                    new_filenames = __info.get("new_filenames")


                    sample_code_base = generate_unique_sample_code()  # 生成唯一的基础样本编号
                    i = 0
                    for new_filename in new_filenames:
                        sample = TaskSample()
                        sample.sort = 0
                        sample.code = sample_code_base +"-"+str(i)
                        sample.user_id = user.get("id")
                        sample.username = user.get("username")
                        sample.task_type = task_type
                        sample.task_code = task_code
                        sample.old_filename = old_filename
                        sample.new_filename = new_filename
                        sample.remark = ""
                        sample.create_time = datetime.now()
                        sample.state = task_state
                        sample.annotation_user_id = 0
                        sample.annotation_state = 0
                        sample.save()
                        i += 1
                        success_count += 1

                else:
                    error_count += 1
            elif upload_type == 4:
                __ret, __msg, __info = upload_utils.upload_sample_labelme(storageDir=g_config.storageDir,
                                                                        task_code=task_code, file=file)
                if __ret:
                    old_filename_prefix = __info["old_filename_prefix"]
                    old_filename_suffix = __info["old_filename_suffix"]
                    if old_filename_suffix == "jpg":
                        labelme_jpg_dict[old_filename_prefix] = __info
                    elif old_filename_suffix == "json":
                        labelme_json_dict[old_filename_prefix] = __info

        if upload_type == 4:

            sample_code_base = generate_unique_sample_code()  # 生成唯一的基础样本编号
            labelName_dict = {}
            i = 0
            for k in labelme_jpg_dict.keys():
                __jpg_info = labelme_jpg_dict.get(k)
                __json_info = labelme_json_dict.get(k)
                if __jpg_info and __json_info:
                    old_filename = __jpg_info.get("old_filename")
                    new_filename = __jpg_info.get("new_filename")

                    annotation_content = []
                    annotation = __json_info.get("annotation")

                    shapes = annotation.get("shapes")
                    imageWidth = annotation.get("imageWidth")
                    imageHeight = annotation.get("imageHeight")

                    for shape in shapes:
                        label = shape.get("label") # drive
                        labelName_dict[label] = labelName_dict.get(label,0) + 1


                        shape_type = shape.get("shape_type")
                        points = shape.get("points")

                        x1 = float(points[0][0])
                        y1 = float(points[0][1])
                        x2 = float(points[1][0])
                        y2 = float(points[1][1])

                        width = x2 - x1
                        height = y2 - y1

                        annotation_content.append({
                            "content": [
                                {"x":x1,"y":y1},
                                {"x":x1,"y":y1},
                                {"x":x2,"y":y2},
                                {"x":x1,"y":y2}
                            ],
                            "rectMask": {
                                "xMin": x1,
                                "yMin": y1,
                                "width": width,
                                "height": height,
                            },
                            "labels": {
                                "labelName": label,
                                "labelColor": "#ff0000",
                                "labelColorRGB": "255,0,0",
                                "visibility": False
                            },
                            "labelLocation": {
                                "x": (x1 + x2) / 2,
                                "y": (y1 + y2) / 2
                            },
                            "contentType": "rect"
                        })

                    sample = TaskSample()
                    sample.sort = 0
                    sample.code = sample_code_base + "-" + str(i)
                    sample.user_id = user.get("id")
                    sample.username = user.get("username")
                    sample.task_type = task_type
                    sample.task_code = task_code
                    sample.old_filename = old_filename
                    sample.new_filename = new_filename
                    sample.remark = ""
                    sample.create_time = datetime.now()
                    sample.state = task_state
                    sample.annotation_user_id = user.get("id")
                    sample.annotation_state = 1
                    sample.annotation_content= json.dumps(annotation_content)
                    sample.save()

                    i += 1
                    success_count += 1

            if task:
                task_labels = []
                task_labels_dict = {}
                try:
                    task_labels = json.loads(task.labels)
                    for task_label in task_labels:
                        task_labels_dict[task_label["labelName"]] = 1

                except:pass

                for labelName in labelName_dict.keys():
                    if not task_labels_dict.get(labelName):
                        task_labels.append({
                            "labelName": labelName,
                            "labelColor": "#ff0000",
                            "labelColorR": "255",
                            "labelColorG": "0",
                            "labelColorB": "0"
                        })
                task.labels = json.dumps(task_labels)
                task.save()


        msg = "成功：%d，失败：%d" % (success_count, error_count)
        if success_count > 0:
            ret = True


    else:
        msg = "request method not supported"

    res = {
        "code": 1000 if ret else 0,
        "msg": msg
    }
    return HttpResponseJson(res)



def api_postDel(request):
    # 检查权限：只有有样本管理权限的用户才能删除图像
    user = readUser(request)
    if not user:
        res = {"code": 0, "msg": "请先登录"}
        return HttpResponseJson(res)

    if not has_permission(user, 'sample_manage'):
        res = {"code": 0, "msg": "无权限删除图像，需要样本管理权限"}
        return HttpResponseJson(res)

    ret = False
    msg = "未知错误"
    if request.method == 'POST':
        params = parse_post_params(request)
        code = params.get("code", "").strip()
        sample = TaskSample.objects.filter(code=code)
        if len(sample) > 0:
            sample = sample[0]
            task_code = sample.task_code

            new_filename_abs = "%s/task/%s/sample/%s" % (g_config.storageDir, sample.task_code, sample.new_filename)
            if os.path.exists(new_filename_abs):
                os.remove(new_filename_abs)
            sample.delete()

            # 删除成功后，更新任务的统计数据
            try:
                task = Task.objects.filter(code=task_code).first()
                if task:
                    sample_count, sample_annotation_count = f_readSampleCountAndAnnotationCount(task_code=task_code)
                    task.sample_count = sample_count
                    task.sample_annotation_count = sample_annotation_count
                    task.save()
            except Exception as e:
                g_logger.error("更新任务统计数据失败: %s" % str(e))

            ret = True
            msg = "删除成功"

        else:
            msg = "该样本不存在！"
    else:
        msg = "request method not supported"

    res = {
        "code": 1000 if ret else 0,
        "msg": msg
    }
    return HttpResponseJson(res)



def api_getInfo(request):
    # 检查权限：标注员或有样本管理权限的用户都可以获取信息
    user = readUser(request)
    if not user:
        res = {"code": 0, "msg": "请先登录"}
        return HttpResponseJson(res)

    if not (has_permission(user, 'annotation_only') or has_permission(user, 'sample_manage')):
        res = {"code": 0, "msg": "无权限获取样本信息"}
        return HttpResponseJson(res)

    ret = False
    msg = "未知错误"

    params = parse_get_params(request)
    task_code = params.get('task_code',"").strip()
    sample_code = params.get('sample_code',"").strip()

    sample = {}
    # 先查询所有样本，了解状态分布
    all_samples = g_database.select("select id, code, state from xc_task_sample where task_code='%s' order by id asc" % task_code)
    state_1_count = len([s for s in all_samples if s['state'] == 1])
    state_0_count = len([s for s in all_samples if s['state'] == 0])

    print(f"任务 {task_code} 样本状态统计: state=1: {state_1_count}, state=0: {state_0_count}, 总计: {len(all_samples)}")

    # 按照id升序排列，确保顺序一致
    sample_data = g_database.select("select * from xc_task_sample where task_code='%s' order by id asc" % task_code)

    if len(sample_data) > 0:
        found_sample = False
        for d in sample_data:
            if d["code"] == sample_code:
                sample_code = d["code"]
                found_sample = True
                break

        # 如果没有找到指定的样本，默认使用第一个
        if not found_sample:
            sample_code = sample_data[0]["code"]

        sample = g_database.select("select xc_task_sample.*,xc_task.labels from xc_task_sample left join xc_task on xc_task_sample.task_code=xc_task.code where xc_task_sample.code='%s' limit 1" % sample_code)
        if len(sample) > 0:
            sample = sample[0]

        ret = True
        msg = "success"

    else:
        msg = "empty data"

    res = {
        "code": 1000 if ret else 0,
        "msg": msg,
        "sample": sample,
        "sample_data": sample_data
    }
    return HttpResponseJson(res)


def api_postSaveAnnotation(request):
    # 检查权限：标注员或有样本管理权限的用户都可以保存标注
    user = readUser(request)
    if not user:
        res = {"code": 0, "msg": "请先登录"}
        return HttpResponseJson(res)

    if not (has_permission(user, 'annotation_only') or has_permission(user, 'sample_manage')):
        res = {"code": 0, "msg": "无权限保存标注"}
        return HttpResponseJson(res)

    ret = False
    msg = "未知错误"
    if request.method == 'POST':
        params = parse_post_params(request)
        sample_code = params.get("sample_code", "").strip()
        annotation_content = params.get("annotation_content", "").strip()
        labels = params.get("labels", "").strip()

        user = readUser(request)
        user_id = user.get("id")
        username = user.get("username")

        sample = TaskSample.objects.filter(code=sample_code).first()
        if sample:
            # 记录之前的标注状态，用于判断是否需要更新统计
            previous_annotation_state = sample.annotation_state
            task_code = sample.task_code

            sample.annotation_user_id = user_id
            sample.annotation_username = username
            sample.annotation_time = datetime.now()
            sample.annotation_content = annotation_content
            sample.annotation_state = 1
            sample.save()

            task = Task.objects.filter(code=task_code).first()
            if task:
                task.labels = labels

                # 如果标注状态发生了变化（从未标注变为已标注），更新任务统计
                if previous_annotation_state != 1:
                    try:
                        sample_count, sample_annotation_count = f_readSampleCountAndAnnotationCount(task_code=task_code)
                        task.sample_count = sample_count
                        task.sample_annotation_count = sample_annotation_count
                    except Exception as e:
                        g_logger.error("更新任务统计数据失败: %s" % str(e))

                task.save()

            ret = True
            msg = "保存标注成功"
        else:
            msg = "该样本不存在！"
    else:
        msg = "request method not supported"

    res = {
        "code": 1000 if ret else 0,
        "msg": msg
    }
    return HttpResponseJson(res)
def api_postDelAnnotation(request):
    # 检查权限：标注员或有样本管理权限的用户都可以删除标注
    user = readUser(request)
    if not user:
        res = {"code": 0, "msg": "请先登录"}
        return HttpResponseJson(res)

    if not (has_permission(user, 'annotation_only') or has_permission(user, 'sample_manage')):
        res = {"code": 0, "msg": "无权限删除标注"}
        return HttpResponseJson(res)

    ret = False
    msg = "未知错误"
    if request.method == 'POST':
        params = parse_post_params(request)
        sample_code = params.get("sample_code", "").strip()

        sample = TaskSample.objects.filter(code=sample_code).first()
        if sample:
            # 记录之前的标注状态，用于判断是否需要更新统计
            previous_annotation_state = sample.annotation_state
            task_code = sample.task_code

            sample.annotation_state = 0
            sample.annotation_content = None
            sample.save()

            # 如果之前是已标注状态，现在变为未标注，更新任务统计
            if previous_annotation_state == 1:
                try:
                    task = Task.objects.filter(code=task_code).first()
                    if task:
                        sample_count, sample_annotation_count = f_readSampleCountAndAnnotationCount(task_code=task_code)
                        task.sample_count = sample_count
                        task.sample_annotation_count = sample_annotation_count
                        task.save()
                except Exception as e:
                    g_logger.error("更新任务统计数据失败: %s" % str(e))

            ret = True
            msg = "删除标注成功"
        else:
            msg = "该样本不存在！"
    else:
        msg = "request method not supported"

    res = {
        "code": 1000 if ret else 0,
        "msg": msg
    }
    return HttpResponseJson(res)